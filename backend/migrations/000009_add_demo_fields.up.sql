-- Add demo-related fields to users table
ALTER TABLE users 
ADD COLUMN onboarding_completed BO<PERSON>EAN DEFAULT false,
ADD COLUMN demo_mode BOOLEAN DEFAULT false,
ADD COLUMN demo_scenario VARCHAR(50) DEFAULT 'startup',
ADD COLUMN is_active BOOLEAN DEFAULT true;

-- Add SSO fields that were referenced in the models
ALTER TABLE users 
ADD COLUMN sso_provider VARCHAR(100),
ADD COLUMN sso_subject VARCHAR(255);

-- Create demo_data table to store demo datasets
CREATE TABLE demo_data (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    scenario VARCHAR(50) NOT NULL,
    data_type VARCHAR(50) NOT NULL, -- 'infrastructure', 'deployments', 'metrics', 'alerts', 'cost'
    data JSONB NOT NULL,
    generated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE
);

-- Create indexes for demo_data table
CREATE INDEX idx_demo_data_user_id ON demo_data(user_id);
CREATE INDEX idx_demo_data_scenario ON demo_data(scenario);
CREATE INDEX idx_demo_data_type ON demo_data(data_type);
CREATE INDEX idx_demo_data_expires_at ON demo_data(expires_at);

-- Add demo metadata to existing tables
ALTER TABLE infrastructure ADD COLUMN is_demo BOOLEAN DEFAULT false;
ALTER TABLE deployments ADD COLUMN is_demo BOOLEAN DEFAULT false;
ALTER TABLE metrics ADD COLUMN is_demo BOOLEAN DEFAULT false;
ALTER TABLE alerts ADD COLUMN is_demo BOOLEAN DEFAULT false;

-- Create indexes for demo flags
CREATE INDEX idx_infrastructure_is_demo ON infrastructure(is_demo);
CREATE INDEX idx_deployments_is_demo ON deployments(is_demo);
CREATE INDEX idx_metrics_is_demo ON metrics(is_demo);
CREATE INDEX idx_alerts_is_demo ON alerts(is_demo);